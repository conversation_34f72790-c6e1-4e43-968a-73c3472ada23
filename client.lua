local ESX = nil
local QBCore = nil

-- 根据配置初始化框架
if Config.Framework == 'esx' then
    ESX = exports["es_extended"]:getSharedObject()
elseif Config.Framework == 'qb' then
    QBCore = exports['qb-core']:GetCoreObject()
end

local xiaohaIsDead = false
local lastReviveTime = 0  -- 新增：记录上次使用AI医护的时间

-- 添加通知事件处理
RegisterNetEvent('xiaohahenshuai:notify')
AddEventHandler('xiaohahenshuai:notify', function(message, type)
    SendNotification(message, type)
end)

-- 玩家重生时重置状态
if Config.Framework == 'esx' then
    AddEventHandler('esx:onPlayerSpawn', function(spawn)
        xiaohaIsDead = false
    end)

    -- 玩家死亡时设置状态和冷却时间
    AddEventHandler('esx:onPlayerDeath', function(data)
        xiaohaIsDead = Config.xiaohaCooldown
        for i = Config.xiaohaCooldown, 0, -1 do
            if type(xiaohaIsDead) ~= "boolean" then
                xiaohaIsDead = i
            else
                return
            end
            Wait(1000)
        end
        if type(xiaohaIsDead) == "number" then
            xiaohaIsDead = true
        end
    end)
elseif Config.Framework == 'qb' then
    -- QB-Core 玩家状态事件
    RegisterNetEvent('hospital:client:Revive', function()
        xiaohaIsDead = false
    end)

    -- QB-Core 死亡事件
    RegisterNetEvent('hospital:client:Death', function()
        xiaohaIsDead = true
    end)
end

-- 新增死亡检测函数
function isDead()
    if Config.xiaohaRevivalSystem == 'osp' then
        -- 尝试多种方式检测OSP死亡状态
        local playerId = GetPlayerServerId(PlayerId())
        local success, ambulanceData = pcall(function()
            return exports.osp_ambulance:GetAmbulanceData(playerId)
        end)

        if success and ambulanceData then
            if Config.xiaohaDebug then
                print("OSP 死亡状态检测: isDead=" .. tostring(ambulanceData.isDead) .. ", inLastStand=" .. tostring(ambulanceData.inLastStand))
            end
            return ambulanceData.isDead or ambulanceData.inLastStand
        else
            if Config.xiaohaDebug then
                print("OSP 系统检测失败，回退到基础死亡检测")
            end
            -- 回退到基础死亡检测
            return IsEntityDead(PlayerPedId()) or GetEntityHealth(PlayerPedId()) <= 0
        end
    elseif Config.Framework == 'qb' then
        local Player = QBCore.Functions.GetPlayerData()
        return Player.metadata["isdead"] or Player.metadata["inlaststand"]
    else -- esx system
        return xiaohaIsDead
    end
    return false
end

-- 检查是否在冷却时间内
function isInCooldown()
    local currentTime = GetGameTimer()
    local timeSinceLastRevive = currentTime - lastReviveTime
    return timeSinceLastRevive < (Config.xiaohaCooldown * 1000)  -- 转换为毫秒
end

-- 获取复活系统类型
function GetRevivalSystem()
    if Config.xiaohaRevivalSystem == 'auto' then
        -- 自动检测复活系统
        if exports['osp_ambulance'] then
            return 'osp'
        elseif exports['esx_ambulancejob'] then
            return 'esx'
        elseif exports['wasabi_ambulance'] then
            return 'wasabi'
        elseif Config.Framework == 'qb' then
            return 'qb'
        else
            -- 默认使用框架自带的复活系统
            return Config.Framework
        end
    else
        return Config.xiaohaRevivalSystem
    end
end

-- 注册复活指令
RegisterCommand(Config.Command, function(source, args, raw)
    -- 检查是否在冷却时间内
    if isInCooldown() then
        local remainingTime = math.ceil((Config.xiaohaCooldown * 1000 - (GetGameTimer() - lastReviveTime)) / 1000)
        SendNotification(string.format(Config.xiaohahenshuaiNotify.cooldown, remainingTime))
        return
    end

    if isDead() then
        if Config.Framework == 'esx' then
            ESX.TriggerServerCallback('xiaohahenshuai:check', function(xiaohaDoctors, xiaohaCanPay)
                handleRevive(xiaohaDoctors, xiaohaCanPay)
            end)
        elseif Config.Framework == 'qb' then
            QBCore.Functions.TriggerCallback('xiaohahenshuai:check', function(xiaohaDoctors, xiaohaCanPay)
                handleRevive(xiaohaDoctors, xiaohaCanPay)
            end)
        end
    elseif type(xiaohaIsDead) == "number" then
        SendNotification(string.format(Config.xiaohahenshuaiNotify.cooldown, xiaohaIsDead))
    else
        SendNotification(Config.xiaohahenshuaiNotify.only_dead)
    end
end)

-- 处理复活逻辑
function handleRevive(xiaohaDoctors, xiaohaCanPay)
    if Config.xiaohaDebug then
        print("客户端收到医生数量: " .. xiaohaDoctors)
        print("客户端收到支付能力: " .. tostring(xiaohaCanPay))
    end
    
    if xiaohaDoctors >= Config.xiaohaMaxDoctors then
        SendNotification(string.format(Config.xiaohahenshuaiNotify.too_many_doctors, Config.xiaohaMaxDoctors), 'error')
        return
    end
    
    if not xiaohaCanPay then
        SendNotification(Config.xiaohahenshuaiNotify.not_enough_money, 'error')
        return
    end
    
    -- 先收费
    TriggerServerEvent('xiaohahenshuai:charge')
    
    -- 根据配置决定是否通知复活时间
    if Config.xiaohaNotifyReviveTime then
        SendNotification(string.format("复活将在 %s 秒后完成", Config.xiaohaReviveTime / 1000))
    end
    
    -- 通知支付类型
    local paymentType = Config.xiaohaPaymentType
    local paymentTypeText = paymentType == 'cash' and "现金" or (paymentType == 'bank' and "银行" or "黑钱")
    SendNotification(string.format("您使用了 %s 进行支付", paymentTypeText))
    
    if Config.xiaohaDebug then
        print("当前使用的通知系统: " .. Config.xiaohaNotifyType)
    end
    
    -- 创建一个变量记录开始时间
    local startTime = GetGameTimer()
    local lastNotificationTime = startTime
    
    -- 开始进度条
    if Config.Framework == 'esx' then
        ESX.Progressbar(Config.xiaohahenshuaiNotify.healing, Config.xiaohaReviveTime)
    elseif Config.Framework == 'qb' then
        QBCore.Functions.Progressbar("revive", Config.xiaohahenshuaiNotify.healing, Config.xiaohaReviveTime, false, true, {
            disableMovement = true,
            disableCarMovement = true,
            disableMouse = false,
            disableCombat = true,
        }, {}, {}, {}, function() -- Done
            PerformRevive()
        end)
    end
    
    -- 创建一个线程来监控进度条完成（仅用于ESX）
    if Config.Framework == 'esx' then
        Citizen.CreateThread(function()
            while true do
                Wait(0)
                
                -- 检查是否达到设定时间
                local elapsedTime = GetGameTimer() - startTime
                if elapsedTime >= Config.xiaohaReviveTime - 50 then
                    PerformRevive()
                    break
                end
            end
        end)
    end
end

-- 执行复活的统一函数
function PerformRevive()
    if Config.xiaohaDebug then
        print("开始执行复活逻辑")
    end

    local revivalSystem = GetRevivalSystem()
    local revived = false

    if Config.xiaohaDebug then
        print("使用的复活系统: " .. revivalSystem)
    end

    -- 尝试使用指定的复活系统
    if revivalSystem == 'osp' then
        if Config.xiaohaDebug then
            print("尝试 OSP 复活方法")
        end

        -- 尝试多种 OSP 复活方法
        local ospMethods = {
            function() TriggerEvent('osp_ambulance:revive') end,
            function() TriggerServerEvent('osp_ambulance:revive') end,
            function() TriggerEvent('osp_ambulance:client:revive') end,
            function() TriggerServerEvent('osp_ambulance:server:revive') end,
            function()
                local playerId = GetPlayerServerId(PlayerId())
                TriggerServerEvent('osp_ambulance:revive', playerId)
            end,
            function()
                -- 尝试直接调用 OSP 导出函数
                if exports.osp_ambulance and exports.osp_ambulance.RevivePlayer then
                    exports.osp_ambulance:RevivePlayer()
                end
            end
        }

        for i, method in ipairs(ospMethods) do
            local success = pcall(method)
            if success then
                if Config.xiaohaDebug then
                    print("OSP 复活方法 " .. i .. " 成功执行")
                end
                revived = true
                break
            else
                if Config.xiaohaDebug then
                    print("OSP 复活方法 " .. i .. " 执行失败")
                end
            end
        end
    elseif revivalSystem == 'esx' then
        local success = pcall(function()
            TriggerEvent('esx_ambulancejob:revive')
            TriggerServerEvent('esx_ambulancejob:revive')
            revived = true
        end)
        if not success and Config.xiaohaDebug then
            print("ESX 复活事件触发失败")
        end
    elseif revivalSystem == 'wasabi' then
        local success = pcall(function()
            TriggerEvent('wasabi_ambulance:revive')
            TriggerServerEvent('wasabi_ambulance:revive')
            revived = true
        end)
        if not success and Config.xiaohaDebug then
            print("Wasabi 复活事件触发失败")
        end
    elseif revivalSystem == 'qb' then
        local success = pcall(function()
            TriggerEvent('hospital:client:Revive')
            TriggerServerEvent('hospital:server:RevivePlayer')
            revived = true
        end)
        if not success and Config.xiaohaDebug then
            print("QB 复活事件触发失败")
        end
    end

    -- 如果指定系统失败，尝试通用复活方法
    if not revived then
        if Config.xiaohaDebug then
            print("指定复活系统失败，尝试通用复活方法")
        end

        -- 强制复活方法
        local playerPed = PlayerPedId()
        local playerId = PlayerId()

        -- 1. 恢复生命值到最大
        local maxHealth = GetEntityMaxHealth(playerPed)
        SetEntityHealth(playerPed, maxHealth)

        -- 2. 清除所有任务和动画
        ClearPedTasksImmediately(playerPed)
        ClearPedSecondaryTask(playerPed)

        -- 3. 停止所有死亡相关的屏幕效果
        StopScreenEffect('DeathFailOut')
        StopScreenEffect('MP_Celeb_Win')
        StopScreenEffect('DeathFailNeutralIn')
        StopScreenEffect('DeathFailMPDark')
        StopScreenEffect('DeathFailMPIn')

        -- 4. 重置玩家状态
        SetPlayerInvincible(playerId, false)
        FreezeEntityPosition(playerPed, false)
        SetEntityCanBeDamaged(playerPed, true)

        -- 5. 确保玩家不在地面上
        local coords = GetEntityCoords(playerPed)
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z + 0.5, false, false, false, true)

        -- 6. 让玩家站起来
        Citizen.CreateThread(function()
            Wait(100)
            TaskPlayAnim(playerPed, "get_up@directional@movement@from_knees@action", "getup_r_0", 8.0, -8.0, -1, 0, 0, false, false, false)
            Wait(2000)
            ClearPedTasks(playerPed)
        end)

        -- 7. 尝试触发 OSP 的状态重置
        if revivalSystem == 'osp' then
            Citizen.CreateThread(function()
                Wait(500)
                -- 尝试多种 OSP 状态重置方法
                local playerId = GetPlayerServerId(PlayerId())

                -- 方法1: 重置玩家状态
                pcall(function() TriggerServerEvent('osp_ambulance:server:resetPlayerState', playerId) end)
                pcall(function() TriggerEvent('osp_ambulance:client:resetState') end)

                -- 方法2: 清除 lastStand 状态
                pcall(function() TriggerServerEvent('osp_ambulance:server:clearLastStand', playerId) end)
                pcall(function() TriggerEvent('osp_ambulance:client:clearLastStand') end)

                -- 方法3: 强制设置为活着状态
                pcall(function() TriggerServerEvent('osp_ambulance:server:setAlive', playerId) end)
                pcall(function() TriggerEvent('osp_ambulance:client:setAlive') end)

                -- 方法4: 尝试直接调用 OSP 导出函数
                pcall(function()
                    if exports.osp_ambulance and exports.osp_ambulance.SetPlayerAlive then
                        exports.osp_ambulance:SetPlayerAlive(playerId)
                    end
                end)

                if Config.xiaohaDebug then
                    print("OSP 状态重置方法执行完成")
                end
            end)
        end

        if Config.xiaohaDebug then
            print("通用复活方法执行完成")
        end

        revived = true
    end

    -- 更新本地死亡状态
    xiaohaIsDead = false

    -- 记录使用AI医护的时间
    lastReviveTime = GetGameTimer()

    -- 如果是 OSP 系统，立即执行额外的状态重置
    if Config.xiaohaRevivalSystem == 'osp' then
        Citizen.CreateThread(function()
            Wait(1000) -- 等待1秒让初始复活生效
            local checkDead = isDead()
            if checkDead then
                if Config.xiaohaDebug then
                    print("OSP 复活后仍处于死亡状态，执行立即重置")
                end
                ForceResetOSPState()
            end
        end)
    end

    -- 延迟检查复活状态
    Citizen.CreateThread(function()
        Wait(2000) -- 等待2秒让复活完全生效

        local stillDead = isDead()
        if stillDead then
            if Config.xiaohaDebug then
                print("检测到玩家仍处于死亡状态，执行强制复活")
            end

            -- 强制复活
            local playerPed = PlayerPedId()
            local playerId = GetPlayerServerId(PlayerId())

            -- 基础复活
            SetEntityHealth(playerPed, GetEntityMaxHealth(playerPed))
            ClearPedTasksImmediately(playerPed)

            -- 专门处理 OSP 系统的 inLastStand 状态
            if Config.xiaohaRevivalSystem == 'osp' then
                ForceResetOSPState()
            end

            -- 强制触发重生事件
            if Config.Framework == 'esx' then
                TriggerEvent('esx:onPlayerSpawn')
            elseif Config.Framework == 'qb' then
                TriggerEvent('hospital:client:Revive')
            end

            xiaohaIsDead = false

            -- 再次检查状态
            Wait(1000)
            local finalCheck = isDead()
            if Config.xiaohaDebug then
                print("最终状态检查: " .. (finalCheck and "仍然死亡" or "复活成功"))
            end
        else
            if Config.xiaohaDebug then
                print("复活成功确认")
            end
        end
    end)

    -- 根据配置决定是否通知支付费用
    if Config.xiaohaNotifyPayment then
        SendNotification(string.format(Config.xiaohahenshuaiNotify.revive_complete, Config.xiaohaPrice))
    end

    -- 新增聊天框通知
    if Config.xiaohaChatNotify then
        TriggerServerEvent('xiaohahenshuai:chatNotify')
    end

    if Config.xiaohaDebug then
        print("复活逻辑执行完成")
    end
end

-- 专门的 OSP 状态重置函数
function ForceResetOSPState()
    if Config.xiaohaRevivalSystem ~= 'osp' then
        return
    end

    if Config.xiaohaDebug then
        print("执行强制 OSP 状态重置")
    end

    local playerId = GetPlayerServerId(PlayerId())
    local playerPed = PlayerPedId()

    -- 1. 基础状态重置
    SetEntityHealth(playerPed, GetEntityMaxHealth(playerPed))
    ClearPedTasksImmediately(playerPed)

    -- 2. 尝试所有可能的 OSP 重置方法
    local resetMethods = {
        -- 服务器端方法
        function() TriggerServerEvent('osp_ambulance:server:revivePlayer', playerId) end,
        function() TriggerServerEvent('osp_ambulance:server:setPlayerAlive', playerId) end,
        function() TriggerServerEvent('osp_ambulance:server:clearLastStand', playerId) end,
        function() TriggerServerEvent('osp_ambulance:server:resetState', playerId) end,
        function() TriggerServerEvent('osp_ambulance:revive', playerId) end,

        -- 客户端方法
        function() TriggerEvent('osp_ambulance:client:setAlive') end,
        function() TriggerEvent('osp_ambulance:client:clearLastStand') end,
        function() TriggerEvent('osp_ambulance:client:resetState') end,
        function() TriggerEvent('osp_ambulance:revive') end,

        -- 导出函数方法
        function()
            if exports.osp_ambulance then
                if exports.osp_ambulance.SetPlayerAlive then
                    exports.osp_ambulance:SetPlayerAlive(playerId)
                end
                if exports.osp_ambulance.ClearLastStand then
                    exports.osp_ambulance:ClearLastStand(playerId)
                end
                if exports.osp_ambulance.RevivePlayer then
                    exports.osp_ambulance:RevivePlayer(playerId)
                end
            end
        end
    }

    for i, method in ipairs(resetMethods) do
        pcall(method)
        if Config.xiaohaDebug then
            print("OSP 重置方法 " .. i .. " 已执行")
        end
    end

    -- 3. 更新本地状态
    xiaohaIsDead = false

    if Config.xiaohaDebug then
        print("OSP 状态重置完成")
    end
end

-- 添加调试命令（可选）
if Config.xiaohaDebug then
    RegisterCommand('resetosp', function()
        ForceResetOSPState()
        SendNotification("OSP 状态已重置")
    end)
end

-- -- 绘制屏幕上的文字
-- function DrawTextOnScreen(text, x, y)
-- 	SetTextFont(0)
-- 	SetTextProportional(1)
-- 	SetTextScale(0.5, 0.5)
-- 	SetTextColour(255, 255, 255, 255)
-- 	SetTextDropShadow(0, 0, 0, 0, 255)
-- 	SetTextEdge(1, 0, 0, 0, 255)
-- 	SetTextDropShadow()
-- 	SetTextOutline()
-- 	SetTextCentre(1)
-- 	SetTextEntry("STRING")
-- 	AddTextComponentString(text)
-- 	DrawText(x, y)
-- end
